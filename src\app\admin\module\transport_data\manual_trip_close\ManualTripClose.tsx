import React, { Component } from 'react';

class ManualTripClose extends Component {
    
    componentDidMount() {
        console.log('ManualTripClose component mounted');
    }

    componentWillUnmount() {
        console.log('ManualTripClose component will unmount');
    }

    handleButtonClick = () => {
        console.log('Manual Trip Close button clicked');
    }

    handleFormSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        console.log('Manual Trip Close form submitted');
    }

    render() {
        console.log('ManualTripClose component rendering');
        
        return (
            <div>
                <div className="px-3 px-sm-4 px-lg-5">
                    <div className="card">
                        <div className="card-header">
                            <h3 className="card-title">Manual Trip Close</h3>
                        </div>
                        <div className="card-body">
                            <div className="row">
                                <div className="col-12">
                                    <p>Manual Trip Close Page - Console logging enabled</p>
                                    
                                    <form onSubmit={this.handleFormSubmit}>
                                        <div className="mb-3">
                                            <label className="form-label">Trip ID</label>
                                            <input 
                                                type="text" 
                                                className="form-control" 
                                                placeholder="Enter Trip ID"
                                                onChange={(e) => console.log('Trip ID changed:', e.target.value)}
                                            />
                                        </div>
                                        
                                        <div className="mb-3">
                                            <label className="form-label">Reason</label>
                                            <textarea 
                                                className="form-control" 
                                                rows={3}
                                                placeholder="Enter reason for manual trip close"
                                                onChange={(e) => console.log('Reason changed:', e.target.value)}
                                            />
                                        </div>
                                        
                                        <div className="d-flex gap-2">
                                            <button 
                                                type="submit" 
                                                className="btn btn-primary"
                                            >
                                                Close Trip Manually
                                            </button>
                                            
                                            <button 
                                                type="button" 
                                                className="btn btn-secondary"
                                                onClick={this.handleButtonClick}
                                            >
                                                Test Console Log
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }
}

export default ManualTripClose;
