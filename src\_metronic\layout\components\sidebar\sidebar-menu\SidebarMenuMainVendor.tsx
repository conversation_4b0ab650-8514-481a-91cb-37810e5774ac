import {SidebarMenuItemWithSub} from './SidebarMenuItemWithSub'
import {SidebarMenuItem} from './SidebarMenuItem'
import {useIntl} from 'react-intl'
import * as authHelper from '../../../../../app/modules/auth/core/AuthHelpers'

const SidebarMenuMainVendor = () => {
    const intl = useIntl()
    const auth = authHelper.getAuth()
    const branch_id = auth?.user?.BRANCH_ID

    return (
        <>

            <SidebarMenuItem
                to='/dashboard'
                icon='element-11'
                title={intl.formatMessage({id: 'MENU.DASHBOARD'})}
                fontIcon='bi-app-indicator'
            />


            <SidebarMenuItemWithSub
                to='/master/data'
                title='Master Data'
                icon='abstract-26'
                fontIcon='bi-database'>

                <SidebarMenuItem to='/master/data/driver' title='Driver' hasBullet={true}/>
                <SidebarMenuItem to='/master/data/vehicle' title='Vehicle' hasBullet={true}/>
                {(branch_id != 62) && (
                    <>
                        <SidebarMenuItem to='/master/data/sim' title='SIM' hasBullet={true}/>
                        <SidebarMenuItem to='/master/data/device' title='Device' hasBullet={true}/>
                    </>
                )}
                <SidebarMenuItem to='/master/data/cab/mapping' title='Cab Mapping' hasBullet={true}/>


            </SidebarMenuItemWithSub>

            <SidebarMenuItemWithSub
                to='/transport/data'
                title='Transport Data'
                icon='delivery'
                fontIcon='bi-truck'>

                <SidebarMenuItem to='/transport/data/reclub' title='Re-Club' hasBullet={true}/>
                <SidebarMenuItem to='/transport/data/otp/sms/form' title='OTP SMS Form' hasBullet={true}/>
                <SidebarMenuItem to='/transport/data/route/order/change' title='Route Order Change' hasBullet={true}/>
                {(branch_id != 62) && (
                    <>
                      <SidebarMenuItem to='/transport/data/trip/close' title='Trip Close' hasBullet={true}/>
                      <SidebarMenuItem to='/transport/data/manual/trip/close' title='Manual Trip Close' hasBullet={true}/>
                      <SidebarMenuItem to='/transport/data/toll/charges' title='Toll Charges' hasBullet={true}/>
                      <SidebarMenuItem to='/transport/data/roster/download' title='Roster Download' hasBullet={true}/>
                    </>
                )}

            </SidebarMenuItemWithSub>

            <SidebarMenuItemWithSub
                to='/tracker/data'
                title='Tracker'
                icon='graph-up'
                fontIcon='bi-graph-up-arrow'
            >
                <SidebarMenuItem to='/tracker/data/vehicle/tracking' title='Vehicle Tracking' hasBullet={true}/>
                <SidebarMenuItem to='/tracker/data/vehicle/travel/path' title='Vehicle Travel Path' hasBullet={true}/>
                <SidebarMenuItem to='/tracker/data/trip/travel/path' title='Trip Travel Path' hasBullet={true}/>
                <SidebarMenuItem to='/tracker/data/real/time' title='RealTime' hasBullet={true}/>
            </SidebarMenuItemWithSub>


            <SidebarMenuItemWithSub
                to='/report/data'
                title='Reports'
                icon='chart-line'
                fontIcon='bi-graph-up'>
                <SidebarMenuItemWithSub to='/report/data/all/report/1' title='All Report' hasBullet={true}>
                    <SidebarMenuItem to='/report/data/overall' title='Overall Report' hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/employee' title='Employee Report' hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/vehicle/roaster' title='Vehicle Roster Report' hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/associate/no/show' title='Associate NoShow' hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/on/time' title='Ontime Report' hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/cancel' title='Cancel Report' hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/accept/reject' title='Accept/Reject' hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/escort' title='Escort Report' hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/otp' title='OTP Report' hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/overtime' title='OverTime Report' hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/asf' title='ASF Report' hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/penalty' title='Penalty Report' hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/toll' title='Toll Report' hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/vehicle' title='Vehicle Report' hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/breakdown' title='Breakdown Report' hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/panic' title='Panic Report' hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/overspeed' title='Overspeed Report' hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/vendor/change' title='Vendor Change Report' hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/feedback' title='Feedback Report' hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/wrong/location' title='Wrong Location Report' hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/sms' title='SMS Report' hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/adhoc' title='Adhoc Report' hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/week/roster' title='Week Roster Report' hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/deactive/route/employee' title='Deactive Route Employee'
                                     hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/gps' title='GPS Report' hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/trip/history' title='Trip History' hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/empty/km' title='Empty KM' hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/empty/approvel/km' title='Empty KM Approvel' hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/video/event' title='Video Event Report' hasBullet={true}/>
                </SidebarMenuItemWithSub>

                {/* <SidebarMenuItemWithSub to='/report/data/mis/report/2' title='MIS Report' hasBullet={true}>
                    <SidebarMenuItem to='/report/data/mis' title='Mis Report' hasBullet={true} />
                    <SidebarMenuItem to='/report/data/fw_mis' title='Fw Mis Report' hasBullet={true} />
                    <SidebarMenuItem to='/report/data/fw_level_1' title='Mis AcceptApprovel' hasBullet={true} />
                    <SidebarMenuItem to='/report/data/fw_level_2' title='Mis ConfirmApprovel' hasBullet={true} />
                    <SidebarMenuItem to='/report/data/fw_final' title='Final Mis Report' hasBullet={true} />
                    <SidebarMenuItem to='/report/data/zoho_mis' title='Zoho Mis Report' hasBullet={true} />
                    <SidebarMenuItem to='/report/data/zohomis_approvel' title='Zoho Mis Approvel' hasBullet={true} />
                    <SidebarMenuItem to='/report/data/toll/payment' title='Toll Payment' hasBullet={true}/>
                    <SidebarMenuItem to='/report/data/driver/misreport/' title='Driver MIS Reports' hasBullet={true}/>
                </SidebarMenuItemWithSub> */}

            </SidebarMenuItemWithSub>


        </>
    )
}

export {SidebarMenuMainVendor}
