import React from 'react'
import {Navigate, Route, Routes} from 'react-router-dom'
import ManualOTP from "../module/transport_data/manual_otp/ManualOTP.tsx";
import OTPSMSForm from "../module/transport_data/otp_sms_form/OTPSMSForm.tsx";
import ReClub from "../module/transport_data/reclub/ReClub.tsx";
import RouteOrderChange from "../module/transport_data/route_order_change/RouteOrderChange.tsx";
import TripClose from "../module/transport_data/trip_close/TripClose.tsx";
import ManualTripClose from "../module/transport_data/manual_trip_close/ManualTripClose.tsx";
import Penalty from "../module/transport_data/penalty/Penalty.tsx";
import TollCharges from "../module/transport_data/toll_charges/TollCharges.tsx";
import Support from "../module/transport_data/support/Support.tsx";
import NewRoster from "../module/transport_data/new_roster/NewRoster.tsx";
import RosterDownload from "../module/transport_data/roster_download/RosterDownload.tsx";
import DeactivateRoute from "../module/transport_data/deactivate_route/DeactivateRoute.tsx";
import EscortCancel from "../module/transport_data/escort_cancel/EscortCancel.tsx";
import ProtectedRoute from "../../Helper/ProtectedRoute.tsx";
import {USER_TYPES} from "../../Helper/ConstantsOne.tsx";
import Adhoc_Request_Report from "../module/transport_data/adhoc/Adhoc_Request_Report.tsx";
import Weekly_Request_Report from "../module/transport_data/week_roster/Weekly_Request_Report.tsx";
import Mask_Transaction_Report from "../module/transport_data/mask_transaction/Mask_Transaction_Report.tsx";
import AdhocWeekly from "../module/transport_data/adhoc_weekly/Adhoc_WeeklyReport.tsx";
import Mask_Call_Log_Report from "../module/transport_data/mask_call_log/Mask_Call_Log_Report.tsx";

const TransportDataPage: React.FC = () => {
    return (
        <Routes>

            <Route
                path='manual/otp'
                element={<ProtectedRoute element={<ManualOTP/>} userType={[USER_TYPES.ADMIN]}/>}
            />
            <Route
                path='otp/sms/form'
                element={<ProtectedRoute element={<OTPSMSForm/>} userType={[USER_TYPES.ADMIN, USER_TYPES.VENDOR]}/>}
            />
            <Route
                path='reclub'
                element={<ProtectedRoute element={<ReClub/>} userType={[USER_TYPES.ADMIN, USER_TYPES.VENDOR]}/>}
            />
            <Route
                path='route/order/change'
                element={<ProtectedRoute element={<RouteOrderChange/>}
                                         userType={[USER_TYPES.ADMIN, USER_TYPES.VENDOR]}/>}
            />
            <Route
                path='trip/close'
                element={<ProtectedRoute element={<TripClose/>} userType={[USER_TYPES.ADMIN, USER_TYPES.VENDOR]}/>}
            />
            <Route
                path='manual/trip/close'
                element={<ProtectedRoute element={<ManualTripClose/>} userType={[USER_TYPES.ADMIN, USER_TYPES.VENDOR]}/>}
            />
            <Route
                path='penalty'
                element={<ProtectedRoute element={<Penalty/>} userType={[USER_TYPES.ADMIN]}/>}
            />
            <Route
                path='toll/charges'
                element={<ProtectedRoute element={<TollCharges/>} userType={[USER_TYPES.ADMIN, USER_TYPES.VENDOR]}/>}
            />
            <Route
                path='support'
                element={<ProtectedRoute element={<Support/>} userType={[USER_TYPES.ADMIN]}/>}
            />
            <Route
                path='new/roster'
                element={<ProtectedRoute element={<NewRoster/>} userType={[USER_TYPES.ADMIN]}/>}
            />
            <Route
                path='roster/download'
                element={<ProtectedRoute element={<RosterDownload/>} userType={[USER_TYPES.ADMIN, USER_TYPES.VENDOR]}/>}
            />
            <Route
                path='deactivate/route'
                element={<ProtectedRoute element={<DeactivateRoute/>} userType={[USER_TYPES.ADMIN]}/>}
            />
            <Route
                path='escort/cancel'
                element={<ProtectedRoute element={<EscortCancel/>} userType={[USER_TYPES.ADMIN]}/>}
            />

            <Route
                path='adhoc'
                element={<ProtectedRoute element={<Adhoc_Request_Report/>} userType={[USER_TYPES.ADMIN]}/>}
            />

            <Route
                path='week/roster'
                element={<ProtectedRoute element={<Weekly_Request_Report/>} userType={[USER_TYPES.ADMIN]}/>}
            />

            <Route
                path='weekly/adhoc'
                element={<ProtectedRoute element={<AdhocWeekly/>} userType={[USER_TYPES.ADMIN]}/>}
            />


            <Route
                path='mask/transaction'
                element={<ProtectedRoute element={<Mask_Transaction_Report/>} userType={[USER_TYPES.ADMIN]}/>}
            />

            <Route
                path='mask/call/log'
                element={<ProtectedRoute element={<Mask_Call_Log_Report/>} userType={[USER_TYPES.ADMIN]}/>}
            />
            <Route
                index
                element={<ProtectedRoute element={<Navigate to='/admin/transport/data/manual/otp'/>}
                                         userType={[USER_TYPES.ADMIN]}/>}
            />

            <Route path='*' element={<Navigate to='/error/404'/>}/>
        </Routes>
    )
}

export default TransportDataPage
